<template>
    <div
        class="markdown-content"
        :data-message-index="messageIndex"
        v-html="getRenderedContent(content, messageIndex)"
    ></div>
</template>

<script>
const marked = require('marked');
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-light.css';

export default {
    name: 'MarkdownRenderer',
    props: {
        content: {
            type: String,
            required: true,
            default: ''
        },
        messageIndex: {
            type: Number,
            required: true,
            default: 0
        }
    },
    data() {
        return {
            renderedContents: new Map() // 缓存渲染
        };
    },
    watch: {
        content: {
            handler() {
                this.renderedContents.clear();
                this.$nextTick(() => {
                    this.addInteractiveFeatures();
                });
            },
            immediate: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.addInteractiveFeatures();
        });
    },
    updated() {
        // 组件更新后添加交互功能
        this.$nextTick(() => {
            this.addInteractiveFeatures();
        });
    },
    methods: {
        // 获取渲染后的内容（用于v-html）
        getRenderedContent(text, messageIndex) {
            if (!text || typeof text !== 'string') {
                return '';
            }

            // 使用缓存避免重复渲染
            const cacheKey = `${messageIndex}-${text}`;
            if (this.renderedContents.has(cacheKey)) {
                return this.renderedContents.get(cacheKey);
            }

            try {
                const renderedHtml = this.renderMarkdown(text);
                this.renderedContents.set(cacheKey, renderedHtml);
                return renderedHtml;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return text; // 降级到纯文本
            }
        },

        // 添加交互功能到所有消息
        addInteractiveFeatures() {
            const element = this.$el;
            if (element) {
                this.addCodeCopyButtons(element);
                this.addTableDownloadButtons(element);
            }
        },

        // 渲染Markdown内容
        renderMarkdown(content) {
            // 配置marked选项
            marked.setOptions({
                highlight: (code, language) => {
                    if (
                        language &&
                        this.isValidLanguageIdentifier(language) &&
                        hljs.getLanguage(language)
                    ) {
                        try {
                            return hljs.highlight(language, code).value;
                        } catch (err) {
                            console.warn('代码高亮失败:', err);
                        }
                    }
                    return hljs.highlightAuto(code).value;
                },
                breaks: true,
                gfm: true,
                tables: true,
                sanitize: false
            });

            return marked(content);
        },

        // HTML转义函数
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // 验证语言标识符是否有效
        isValidLanguageIdentifier(lang) {
            if (!lang || typeof lang !== 'string') {
                return false;
            }

            const normalizedLang = lang.toLowerCase().trim();

            // 语言标识符应该是简短的单词，不应包含代码内容
            return !(
                normalizedLang.length > 20 ||
                normalizedLang.includes('(') ||
                normalizedLang.includes(')') ||
                normalizedLang.includes('{') ||
                normalizedLang.includes('}') ||
                normalizedLang.includes(' ') ||
                normalizedLang.includes('\n') ||
                normalizedLang.includes(';') ||
                normalizedLang.includes('=') ||
                normalizedLang.includes('[') ||
                normalizedLang.includes(']')
            );
        },

        // 添加代码复制按钮
        addCodeCopyButtons(container) {
            const preElements = container.querySelectorAll('pre');
            preElements.forEach((pre, index) => {
                if (pre.querySelector('.copy-button')) return; // 避免重复添加

                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    <span class="copy-text">复制</span>
                `;
                copyButton.title = '复制代码';

                copyButton.addEventListener('click', () => {
                    const code = pre.querySelector('code');
                    if (code) {
                        this.copyToClipboard(code.textContent);
                        this.showCopySuccess(copyButton);
                    }
                });

                pre.style.position = 'relative';
                pre.appendChild(copyButton);
            });
        },

        // 添加表格下载按钮
        addTableDownloadButtons(container) {
            const tables = container.querySelectorAll('table');
            tables.forEach((table, index) => {
                if (table.parentNode.querySelector('.table-download-button')) return; // 避免重复添加

                const tableWrapper = document.createElement('div');
                tableWrapper.className = 'table-wrapper';

                const downloadButton = document.createElement('button');
                downloadButton.className = 'table-download-button';
                downloadButton.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    <span class="download-text">下载</span>
                `;
                downloadButton.title = '下载表格为Excel文件';

                downloadButton.addEventListener('click', () => {
                    this.downloadTableAsExcel(table, `table_${index + 1}`);
                });

                // 包装表格
                table.parentNode.insertBefore(tableWrapper, table);
                tableWrapper.appendChild(table);
                tableWrapper.appendChild(downloadButton);
            });
        },

        // 复制到剪贴板
        copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text);
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                document.execCommand('copy');
                textArea.remove();
            }
        },

        // 显示复制成功状态
        showCopySuccess(button) {
            const originalHtml = button.innerHTML;
            button.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
                <span class="copy-text">已复制</span>
            `;
            button.classList.add('copied');

            setTimeout(() => {
                button.innerHTML = originalHtml;
                button.classList.remove('copied');
            }, 2000);
        },

        // 下载表格为Excel
        downloadTableAsExcel(table, filename) {
            // 创建工作簿
            const rows = [];
            const headerRow = [];

            // 提取表头
            const headers = table.querySelectorAll('thead th');
            headers.forEach((th) => {
                headerRow.push(th.textContent.trim());
            });
            if (headerRow.length > 0) {
                rows.push(headerRow);
            }

            // 提取数据行
            const bodyRows = table.querySelectorAll('tbody tr');
            bodyRows.forEach((tr) => {
                const row = [];
                const cells = tr.querySelectorAll('td');
                cells.forEach((td) => {
                    row.push(td.textContent.trim());
                });
                if (row.length > 0) {
                    rows.push(row);
                }
            });

            // 创建CSV内容
            const csvContent = rows
                .map((row) => row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(','))
                .join('\n');

            // 添加BOM以支持中文
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], {
                type: 'text/csv;charset=utf-8;'
            });

            // 创建下载链接
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }
};
</script>

<style lang="less">
.markdown-content {
    line-height: 1.6;
    color: #333;

    // 标题样式 - 优化层次和缩进
    h1 {
        font-size: 24px;
        font-weight: bold;
        margin: 20px 0 16px 0;
        padding-left: 0;
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 8px;
        line-height: 1.3;

        &:first-child {
            margin-top: 0;
        }
    }

    h2 {
        font-size: 20px;
        font-weight: bold;
        margin: 18px 0 14px 0;
        padding-left: 8px;
        color: #34495e;
        border-bottom: 1px solid #bdc3c7;
        padding-bottom: 6px;
        line-height: 1.3;
    }

    h3 {
        font-size: 18px;
        font-weight: bold;
        margin: 16px 0 12px 0;
        padding-left: 16px;
        color: #34495e;
        line-height: 1.3;
    }

    h4 {
        font-size: 16px;
        font-weight: bold;
        margin: 14px 0 10px 0;
        padding-left: 24px;
        color: #34495e;
        line-height: 1.3;
    }

    h5,
    h6 {
        font-size: 15px;
        font-weight: bold;
        margin: 12px 0 8px 0;
        padding-left: 32px;
        color: #34495e;
        line-height: 1.3;
    }

    // 段落样式 - 优化缩进和间距
    p {
        margin: 12px 0;
        padding-left: 8px;
        line-height: 1.7;
        text-indent: 0;
        color: #333;

        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }

        // 嵌套在列表中的段落
        li & {
            margin: 4px 0;
            padding-left: 0;
        }

        // 嵌套在引用中的段落
        blockquote & {
            padding-left: 0;
        }
    }

    // 强调样式
    strong {
        font-weight: bold;
        color: #2c3e50;
    }

    em {
        font-style: italic;
        color: #7f8c8d;
    }

    // 列表样式 - 优化缩进层次
    ul,
    ol {
        margin: 12px 0;
        padding-left: 32px;

        li {
            margin: 6px 0;
            line-height: 1.6;
            padding-left: 4px;

            // 嵌套列表
            ul,
            ol {
                margin: 6px 0;
                padding-left: 24px;

                li {
                    margin: 4px 0;

                    // 三级嵌套
                    ul,
                    ol {
                        padding-left: 20px;
                    }
                }
            }
        }
    }

    // 链接样式
    a {
        color: #0265fe;
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: all 0.2s ease;

        &:hover {
            border-bottom-color: #0265fe;
        }
    }

    // 行内代码样式
    code {
        background: rgba(175, 184, 193, 0.2);
        color: #476582;
        padding: 3px 6px;
        border-radius: 4px;
        font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
            'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
        font-size: 0.9em;
        font-weight: 500;
        border: 1px solid rgba(175, 184, 193, 0.3);
        letter-spacing: 0.025em;
    }

    // 代码块样式
    pre {
        background: rgba(248, 249, 250, 0.8);
        border: 1px solid rgba(175, 184, 193, 0.3);
        border-radius: 10px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
        position: relative;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        backdrop-filter: blur(10px);
        tab-size: 2;

        code {
            background: none;
            color: #2d3748;
            padding: 0;
            border: none;
            border-radius: 0;
            font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
                'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.5;
            font-weight: 400;
            letter-spacing: 0.025em;
            tab-size: 2;
        }
    }

    // 代码复制按钮样式 - 统一设计
    .copy-button {
        position: absolute;
        top: 12px;
        right: 12px;
        background: transparent;
        border: none;
        border-radius: 6px;
        padding: 5px 10px;
        font-size: 13px;
        color: #0265fe;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        font-weight: 500;

        &:hover {
            background: rgba(2, 101, 254, 0.08);
            border-radius: 6px;
        }

        &.copied {
            color: #28a745;

            &:hover {
                background: rgba(40, 167, 69, 0.08);
            }
        }

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .copy-text {
            font-weight: 500;
            white-space: nowrap;
        }
    }

    // 表格包装器样式
    .table-wrapper {
        position: relative;
        margin: 16px 0;
    }

    // 引用样式 - 优化设计
    blockquote {
        border-left: 4px solid #0265fe;
        margin: 16px 0;
        padding: 16px 20px;
        background: rgba(2, 101, 254, 0.05);
        border-radius: 0 10px 10px 0;
        color: #555;
        font-style: italic;
        backdrop-filter: blur(5px);

        p {
            margin: 0;
            padding-left: 0;
        }
    }

    // 表格样式 - 重新设计
    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 16px 0;
        background-color: transparent;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

        thead {
            background: rgba(255, 255, 255, 0.8);
        }

        th {
            padding: 14px 18px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);

            // 移除竖向分割线
            border-left: none;
            border-right: none;

            &:first-child {
                border-top-left-radius: 10px;
            }

            &:last-child {
                border-top-right-radius: 10px;
            }
        }

        td {
            padding: 14px 18px;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
            line-height: 1.6;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(5px);

            // 移除竖向分割线
            border-left: none;
            border-right: none;
        }

        tbody tr:hover td {
            background: rgba(255, 255, 255, 0.8);
        }

        tbody tr:last-child td {
            border-bottom: none;

            &:first-child {
                border-bottom-left-radius: 10px;
            }

            &:last-child {
                border-bottom-right-radius: 10px;
            }
        }
    }

    // 表格下载按钮样式 - 统一设计
    .table-download-button {
        position: absolute;
        top: 12px;
        right: 12px;
        background: transparent;
        border: none;
        border-radius: 6px;
        padding: 5px 10px;
        font-size: 13px;
        color: #0265fe;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        z-index: 10;
        font-weight: 500;

        &:hover {
            background: rgba(2, 101, 254, 0.08);
            border-radius: 6px;
        }

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .download-text {
            font-weight: 500;
            white-space: nowrap;
        }
    }

    // 分割线样式
    hr {
        border: none;
        height: 1px;
        background: #e8e8e8;
        margin: 16px 0;
    }

    // 图片样式
    img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 8px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // 复选框列表样式
    input[type='checkbox'] {
        margin-right: 8px;
        transform: scale(1.2);
    }
}
</style>
